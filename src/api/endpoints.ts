export const ENDPOINTS = {
  COMMON_CODES: '/common-codes',
  RECORD: {
    VITAL_FROM: '/vital-from', // バイタルデータ連携設定取得API
    VITAL_DATA: '/vital-data', // バイトルデータ登録API
    THUMBNAIL_DATA: '/thumn-data', // サムネデータ取得API
    DAILY_VITAL_DATA: '/daily-vital-data', // 日別バイタルデータ取得API
  },
  HOME: {
    ORGANIZER_LIST: '/home/<USER>',
    SET_ORGANIZER: '/home/<USER>',
    GET_STEP: '/home/<USER>',
    GET_MISSION: '/mission/home-top-mission-list',
    LOGIN_BONUS_POINT: '/startup/login-bonus-point',
    PHOTOS_CLAIM: '/photos/claim',
    ORGANIZER_INFO: '/organizer/info',
  },
  HEALTHY: {
    ASSET_GRAPH: '/risk/asset-graph',
    JUDGMENT_RESULT: '/risk/judgment-result',
    MISSION: '/risk/mission',
  },
  USER: {
    BASE: '/users',
    AUTH: '/auth',
    USER: '/user',
  },
  RISK: {
    BASE: '/risk',
    ONOFFSTATE: '/on-off-state',
    JUDGEMENTDATECALC: '/judgment-date-calc',
  },
  GRAPH: '/graph',
  HEALTH_CHECKUP: {
    HEALTH_INFO: '/health-checkup/health-info', //マイナポータル確認完了(連携)
    CONNECTION_SERVICE: '/health-checkup/connection/service', //接続サービス設定取得
    MEDICAL_EXAM: '/health-checkup/medical-exam', // 健康診断API test
    WELLCLE_UPLOAD: '/health-checkup/wellcle/upload', // 生活習慣病チェック推定(連携)
    HEALTH_INFO_REQUEST: '/myna/health-info-request', // 本人複数同意・本人確認要求(連携)
  },
  HEALTH_CHECKUP_INPUT: '/health-checkup',
  GAMIFICATION: '/gamification',
  GOAL: {
    STEP_TARGET: '/step-target',
    PERIOD_COMPUTE: '/period-compute',
  },
  CHECKUP_DATE_RECORD: {
    TYPE_LIST: '/checkup-date-record/type-list',
    LIST: '/checkup-date-record/list',
  },
  FRIEND: {
    STEP_RANKING: 'friend/step-ranking',
    CANCEL_FRIEND: 'friend/cancel-friend',
    INVITE_LINK: 'friend/invite-friend',
    SEND_CANCEL_YELL: 'friend/send-cancel-yell',
    FRIENDS: 'friend/friends',
    ADD_FRIEND: 'friend/add-friend',
  },
  DATA_CONNECT: {
    FITBIT_RECRECT: '/device/fitbit-redirect',
    DEVICE_SETTING: '/device/setting',
    VITAL_DATA_FROM: '/device/vital-history?',
    VITAL_SETTING: '/device/vital-setting',
    SERVICE_SETTING: '/device/service-setting',
    FITBIT_TOKEN: '/device/fitbit-access-token',
    VITAL_FROM: '/vital/vital-from',
    SYNC_VITAL: '/vital/sync-vital-data',
    OMRON_RECRECT: '/device/omron-redirect',
    OMRON_TOKEN: '/device/omron-access-token',
    VITAL_TYPE: '/device/vital-type',
  },
  POINT: {
    HOME_POINT_CARD_INFO: '/points/home',
    GET_POINT_HISTORY_LIST: '/points/history',
    GET_POINT_CARD_INFO: '/points',
    GET_LOTTERY_INFO: '/points/lottery',
    GET_RESET_INFO: '/points/reset',
    GET_EVENT_HOME: '/events/home',
  },
  MISSION: {
    MISSION_LIST: 'mission/mission-list',
    MISSION_DETAIL: 'mission/mission-detail',
    MISSION_POPUP_DETAIL: 'mission/mission-achieve-get',
    ACHIEVE_UPDATE: 'mission/mission-achieve-update',
    MISSION_READ_DETAIL: 'mission/mission-read-detail',
    MISSION_SECTION_LIST: 'mission/mission-section-list',
    QUIZ_MISSION_DETAIL: 'mission/quiz-mission-detail',
    QUIZ_ACHIEVE_UPDATE: 'mission/quiz-achieve-update',
  },

  REGISTER: {
    TEMP_TOKEN: 'auth/temp-token',
    ORGANIZER_INFO: 'organizer/info',
    FRIEND_INFO: 'friend/info',
    ORGANIZER_SETUP: 'organizer/setup-detail',
    ACCOUNT_PROFILE: 'account/profile',
    LITA_TOKEN: 'auth/lita/token',
    AUTH_LOGIN: 'auth/login',
    REGISTER_ANONYMOUS: 'user/register/anonymous',
    USER_ORGANIZERS: 'user/organizers',
    USER_PROFILE: 'user/profile',
    REFRESH_TOKEN: 'auth/refresh-token',
    USER_SMS_CODE: 'user/sms_code',
    USER_SMS_CODE_VERIFY: 'user/sms_code/verify',
    REGISTER_WITH_AUTH: 'user/register/with-auth',
    ORGANIZER_TERMS: 'organizer/terms',
    ADD_FRIEND: 'friend/add-friend',
  },
  EVENT: 'events',
  ORGANIZER_GEO_LOCATION: {
    ORGANIZER_LOCATIONS: '/map/organizer-locations',
  },
  MINIAPP: {
    GET_MINIAPP_URL: '/miniapp/path/[id]',
  },
  LOTTERY_PRIZE: {
    LOTTERY_HISTORY: '/lottery/history_lottery',
    LOTTERY_OPEN: '/lottery/getOpenLottery',
    LOTTERY_USER_CHOICE: '/lottery/user_lottery_choice',
    LOTTERY_PRIZE_LIST: '/lottery/prize_list',
    LOTTERY_DETAIL: '/lottery/lottery_detail',
    LOTTERY_USER_RESULT: '/lottery/user_lottery_result',
    LOTTERY_GIFTEE: '/lottery/Issue_gifteeBox',
  },
  PRIZE_SELECT: {
    PRIZE_LIST: '/lottery/prize_list', //景品一覧取得
    PRIZE_DETAIL: '/lottery/prize_detail', //景品詳細取得
    USER_LOTTERY_CHOICE: '/lottery/user_lottery_choice', //ユーザ抽選景品選択取得API
    REG_LOTTERY_CHOICE: '/lottery/reg_lottery_choice', //ユーザ抽選景品選択登録API
    DEL_LOTTERY_CHOICE: '/lottery/del_lottery_choice', //ユーザ抽選景品選択削除API
    UPD_LOTTERY_CHOICE: '/lottery/upd_lottery_choice', //ユーザ抽選景品選択削除API
    GET_APP_LOTTERY: '/lottery/getAppLottery', //抽選応募情報取得
  },
  WALKING_COURSE: {
    LIST: '/walking-courses/walking-courses',
    DETAIL: '/walking-courses/walking-courses/[id]',
    WALKING_COURSE_LIST: '/walking-courses/walking-courses/list', //ウォーキングコース情報一覧API
    WALKING_COURSE_NAME: '/walking-courses/walking-courses/name', //ウォーキングコース名検索API
    MAP_WALKING_COURSE: '/walking-courses/stamp-spots', //マップウォーキングコース取得API
    RECORD_STAMP: '/walking-courses/record-stamp', //ウォーキングコース名検索API
  },
  MENU: {
    LEVEL_INFO: 'menu/level-info',
    SEND_PUSH_FLG: 'menu/push-send-flg',
    GET_PUSH_FLG: 'lottery/push_send_flg',
    DO_TERMINATE: '/terminate',
    GET_ORGANIZERS: '/organizer-names',
    STARTUP: '/startup',
    MENU_TERMS: 'menu/terms',
  },
  RANKING: {
    USER_RANKING_INFO_LIST: '/ranking/user-rankinginfo-list',
    STEP_GROUP_DAILY_RANKING_LIST: '/ranking/step-groupdailyranking-list',
    STEP_DAILY_RANKING_LIST: '/ranking/step-dailyranking-list',
    STEP_GROUP_WEEKLY_RANKING_LIST: '/ranking/step-groupweeklyranking-list',
    STEP_WEEKLY_RANKING_LIST: '/ranking/step-weeklyranking-list',
    MISSION_DAILY_RANKING_LIST: '/ranking/mission-dailyranking-list',
    MISSION_WEEKLY_RANKING_LIST: '/ranking/mission-weeklyranking-list',
    ORGANIZER_RANKING_LIST: '/ranking/organizer-ranking-list',
    RANKING_EXCL_USER: '/ranking/ranking-excl-user',
  },
  MAP: {
    PINGS: '/map/pins',
    RESULTS: '/map/results',
    SEARCH_CANDIDATES: '/map/search-candidates',
    POSTS: '/map/posts',
    FAVORITE: '/map/favorite',
    FAVORITE_PINS: '/map/favorite-pins',

  },
  COUPON: {
    SEARCH: '/coupons/search', //クーポン検索
    HOME_COUPON: '/coupons/home',
    RECOMMEND: '/coupons/recommendations', //クーポンおすすめ
    POPULAR: 'coupons/popular',
    KEYWORD_SEARCH: '/coupons/keyword-search', //クーポンキーワード候補検索
    COUPON_DETAIL: '/coupons/[id]', //クーポン詳細
    SHOP_DETAIL: '/coupons/shops/[id]', //店舗詳細
    USE_COUPON: '/coupons/[id]/use', //クーポン使用
  },
  //  Other modules...
};
