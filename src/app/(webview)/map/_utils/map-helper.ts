import { codeTypeList, useCommonCode } from '@/hooks/use-common-code';

export function useMapHelper() {
  const commonCode = useCommonCode();
  const mapTypeOptions = commonCode.getOptions(codeTypeList.MAP_TYPE);
  const mapTypeToKey = (mapType: string) => {
    const codeList = commonCode.getType(codeTypeList.MAP_TYPE)?.codeList || [];
    const code = codeList.find((code) => code.codeName === mapType);
    return code?.codeKey || '1';
  };
  const keyToMapType = (key: string) => {
    const codeList = commonCode.getType(codeTypeList.MAP_TYPE)?.codeList || [];
    const code = codeList.find((code) => code.codeKey === key);
    return code?.codeName || '';
  };

  return {
    mapTypeOptions,
    mapTypeToKey,
    keyToMapType,
  };
}
