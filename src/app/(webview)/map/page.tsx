//import dynamic from 'next/dynamic';
// const MapComponent = dynamic(() => import('./_components/map'), { ssr: false });

import { cn } from '@/lib/utils';
import { StampSpotProvider } from './_context/use-stamp-spot-state';
import MapComponent from '@/components/shared/map/map';
import MapDrawer from './_components/map-drawer';
import { useQuery } from '@tanstack/react-query';



  const {
    data: shopData,
    isLoading,
    isSuccess,
    error,
  } = useQuery({
    queryKey: ['mapPins'],
    queryFn: () => mapAPI.shopDetail(id),
    enabled: !!id,
  });





export function MapPageContent() {
   return (
     <div className="relative h-[100vh]">
       <div
         className={cn(
           'transition-all duration-300 h-full w-full',
           showShopDetail ? 'translate-y-[-25%]' : 'translate-y-0',
         )}
       >
         <MapComponent
           markers={markers}
           lines={lines}
           onMarkerClick={(marker: Marker) => {
             handleClickMarker(marker);
           }}
         />
       </div>
       <MapDrawer
         show={showShopDetail}
         data={shopData?.shopInfo}
       />
     </div>
   );
}

export default function MapPage() {
  return (
    <StampSpotProvider>
      <MapPageContent />
    </StampSpotProvider>
  );
}
